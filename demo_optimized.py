from openai import OpenAI
import os
from typing import Optional

def create_openai_client(api_key: Optional[str] = None, base_url: Optional[str] = None) -> OpenAI:
    """
    创建OpenAI客户端
    
    Args:
        api_key: OpenAI API密钥，如果不提供则从环境变量读取
        base_url: API基础URL，用于自定义端点
    
    Returns:
        OpenAI客户端实例
    """
    if api_key is None:
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("请设置OPENAI_API_KEY环境变量或提供api_key参数")
    
    client_kwargs = {"api_key": api_key}
    if base_url:
        client_kwargs["base_url"] = base_url
    
    return OpenAI(**client_kwargs)

def chat_with_streaming(client: OpenAI, 
                       message: str, 
                       model: str = "gpt-4o",
                       system_message: Optional[str] = None) -> None:
    """
    使用流式响应与AI对话
    
    Args:
        client: OpenAI客户端
        message: 用户消息
        model: 使用的模型名称
        system_message: 可选的系统消息
    """
    try:
        messages = []
        
        # 添加系统消息（如果提供）
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        # 添加用户消息
        messages.append({"role": "user", "content": message})
        
        # 创建流式完成
        stream = client.chat.completions.create(
            model=model,
            messages=messages,
            stream=True,
            temperature=0.7,
            max_tokens=1000
        )
        
        print("AI响应: ", end="", flush=True)
        
        # 处理流式响应
        for chunk in stream:
            if chunk.choices and chunk.choices[0].delta.content:
                print(chunk.choices[0].delta.content, end="", flush=True)
        
        print()  # 换行
        
    except Exception as e:
        print(f"发生错误: {e}")

def main():
    """
    主函数
    """
    try:
        # 创建客户端（可以根据需要修改API密钥和基础URL）
        client = create_openai_client(
            api_key="9a63bfb2-d9d2-4cc6-99d5-e8ff010e3eaf",  # 建议使用环境变量
            base_url="http://192.168.10.251:8081/v1"  # 如果使用本地部署的模型
        )
        
        # 进行对话
        chat_with_streaming(
            client=client,
            message="你是谁？",
            model="gpt-4o",
            system_message="你是一个有用的AI助手。"
        )
        
    except Exception as e:
        print(f"程序执行失败: {e}")
        print("\n使用建议:")
        print("1. 设置环境变量: export OPENAI_API_KEY='your-actual-api-key'")
        print("2. 或者直接在代码中替换api_key参数")
        print("3. 如果使用本地模型，请取消注释base_url参数")

if __name__ == "__main__":
    main()